[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "futures-analyzer-final"
version = "0.2.0"
description = "专业的期货技术分析工具库 - 最终版本"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Futures Analysis Team", email = "<EMAIL>"}
]
keywords = ["futures", "trading", "technical-analysis", "finance", "quantitative"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Financial and Insurance Industry",
    "Topic :: Office/Business :: Financial :: Investment",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
requires-python = ">=3.7"
dependencies = [
    "pandas>=1.3.0",
    "numpy>=1.20.0",
    "python-dateutil>=2.8.0"
]

[project.urls]
Homepage = "https://github.com/futures-analysis/futures-analyzer-final"
Repository = "https://github.com/futures-analysis/futures-analyzer-final"
Documentation = "https://futures-analyzer-final.readthedocs.io/"
"Bug Reports" = "https://github.com/futures-analysis/futures-analyzer-final/issues"

[project.scripts]
futures-analyzer = "main:main"

[tool.setuptools]
py-modules = ["futures_analyzer", "local_data_loader", "local_technical_indicators", "main"]
