#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货技术分析库 v0.2 - 最终版本
简化的期货技术分析工具，支持直接输入合约代码进行分析
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple, List

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from local_data_loader import LocalDataLoader
from local_technical_indicators import LocalTechnicalIndicators

# 配置日志
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FuturesAnalyzer:
    """
    期货技术分析器 - 最终版本

    功能：
    1. 支持 交易所/合约代码 格式输入
    2. 自动选择最佳K线周期进行分析
    3. 结果保存到文件
    4. 提供Python库形式的API
    """

    def __init__(self, data_dir: str = "/Users/<USER>/Downloads/data_index_0704",
                 output_dir: str = "analysis_results"):
        """
        初始化分析器

        Args:
            data_dir: 数据目录路径
            output_dir: 输出目录路径
        """
        self.data_loader = LocalDataLoader(data_dir)
        self.calculator = LocalTechnicalIndicators()
        self.output_dir = output_dir
        self.supported_exchanges = ['SHFE', 'DCE', 'ZCE', 'CFFEX', 'GFEX']

        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        logger.info("期货分析器初始化完成")
    
    def validate_symbol(self, symbol: str) -> Tuple[bool, str]:
        """验证合约代码"""
        if not symbol or '/' not in symbol:
            return False, "格式错误：请使用 '交易所/合约代码' 格式，如 CFFEX/IF2509"

        try:
            parts = symbol.split('/')
            if len(parts) != 2:
                return False, "格式错误：请使用 '交易所/合约代码' 格式"

            exchange, contract = parts[0].strip().upper(), parts[1].strip()

            if exchange not in self.supported_exchanges:
                return False, f"不支持的交易所: {exchange}，支持的交易所: {', '.join(self.supported_exchanges)}"

            # 检查交易所是否存在
            available_exchanges = self.data_loader.list_available_exchanges()
            if exchange not in available_exchanges:
                return False, f"交易所 {exchange} 无可用数据"

            # 检查合约是否存在
            contracts = self.data_loader.list_contracts_by_exchange(exchange)
            if contract not in contracts:
                return False, f"合约 {contract} 在 {exchange} 中不存在"

            return True, ""

        except Exception as e:
            return False, f"验证失败: {str(e)}"
    
    def analyze_contract(self, exchange: str, contract: str, period: str) -> Optional[Dict[str, Any]]:
        """分析单个合约的单个周期"""
        try:
            # 加载数据
            data = self.data_loader.load_contract_data(exchange, contract, period)

            if data is None or len(data) < 60:
                return None

            # 计算技术指标
            indicators = self.calculator.calculate_all_indicators(data)

            if indicators is None:
                return None

            # 生成简化分析
            analysis = self.generate_simple_analysis(indicators, data, exchange, contract, period)

            return analysis

        except Exception as e:
            logger.error(f"分析 {period} 失败: {e}")
            return None

    def generate_simple_analysis(self, indicators, data, exchange, contract, period):
        """生成简化分析"""
        current_price = data['close'].iloc[-1]
        current_volume = data['volume'].iloc[-1]
        current_oi = data['open_interest'].iloc[-1]

        analysis = {
            'timestamp': datetime.now(),
            'exchange': exchange,
            'contract': contract,
            'period': period,
            'current_price': current_price,
            'current_volume': current_volume,
            'current_open_interest': current_oi,
            'data_period': f"{str(data.index[0])[:10]} 至 {str(data.index[-1])[:10]}",
            'total_records': len(data)
        }

        # 趋势分析
        trend = indicators.get('trend', {})
        analysis['trend'] = {
            'overall_direction': trend.get('overall_direction', '震荡'),
            'short_term': self.describe_trend(trend.get('short_trend', 0)),
            'medium_term': self.describe_trend(trend.get('medium_trend', 0)),
            'long_term': self.describe_trend(trend.get('long_trend', 0)),
            'strength': trend.get('trend_strength', 0.5)
        }

        # RSI分析
        rsi = indicators.get('rsi', [])
        if isinstance(rsi, np.ndarray) and len(rsi) > 0:
            try:
                rsi_current = float(rsi[-1])
                if not np.isnan(rsi_current):
                    if rsi_current > 70:
                        rsi_status = "超买"
                    elif rsi_current < 30:
                        rsi_status = "超卖"
                    else:
                        rsi_status = "正常"
                else:
                    rsi_current = None
                    rsi_status = "数据不足"
            except:
                rsi_current = None
                rsi_status = "数据不足"
        else:
            rsi_current = None
            rsi_status = "数据不足"

        analysis['momentum'] = {
            'rsi': {'current': rsi_current, 'status': rsi_status}
        }

        # 波动性分析
        volatility = indicators.get('volatility', {})
        analysis['volatility'] = {
            'level': volatility.get('volatility_level', '未知'),
            'historical_volatility': volatility.get('historical_volatility', 0),
            'atr_volatility': volatility.get('atr_volatility', 0)
        }

        # 成交量分析
        volume_data = indicators.get('volume', {})
        obv = volume_data.get('obv', [])

        # 安全地检查OBV趋势
        obv_trend = '下降'
        try:
            if isinstance(obv, np.ndarray) and len(obv) > 1:
                if obv[-1] > obv[-2]:
                    obv_trend = '上升'
            elif isinstance(obv, list) and len(obv) > 1:
                if obv[-1] > obv[-2]:
                    obv_trend = '上升'
        except:
            obv_trend = '未知'

        # 安全地检查成交量比率
        volume_ratio = volume_data.get('volume_ratio', 1)
        if isinstance(volume_ratio, (np.ndarray, list)) and len(volume_ratio) > 0:
            volume_ratio = volume_ratio[-1]

        analysis['volume'] = {
            'status': '正常' if volume_ratio < 2 else '放量',
            'obv_trend': obv_trend
        }

        return analysis

    def describe_trend(self, trend_value):
        """描述趋势"""
        if trend_value > 0.1:
            return "上升"
        elif trend_value < -0.1:
            return "下降"
        else:
            return "震荡"

    def save_analysis_to_file(self, analysis: Dict[str, Any]) -> Optional[str]:
        """保存分析结果到文件"""
        try:
            timestamp = analysis['timestamp'].strftime("%Y%m%d_%H%M%S")
            filename = f"analysis_{analysis['exchange']}_{analysis['contract']}_{analysis['period']}_{timestamp}.txt"
            file_path = os.path.join(self.output_dir, filename)

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"期货技术分析报告 v0.2\n")
                f.write("="*80 + "\n")
                f.write(f"合约代码: {analysis['exchange']}/{analysis['contract']}\n")
                f.write(f"数据周期: {analysis['period']}\n")
                f.write(f"分析时间: {analysis['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"当前价格: {analysis['current_price']:.2f}\n")
                f.write(f"当前成交量: {analysis['current_volume']:,}\n")
                f.write(f"当前持仓量: {analysis['current_open_interest']:,}\n")
                f.write(f"数据范围: {analysis['data_period']}\n")
                f.write(f"数据量: {analysis['total_records']} 条记录\n")
                f.write("="*80 + "\n\n")

                # 趋势分析
                trend = analysis['trend']
                f.write("📈 趋势分析:\n")
                f.write(f"  整体方向: {trend['overall_direction']}\n")
                f.write(f"  短期趋势: {trend['short_term']}\n")
                f.write(f"  中期趋势: {trend['medium_term']}\n")
                f.write(f"  长期趋势: {trend['long_term']}\n")
                f.write(f"  趋势强度: {trend['strength']:.2f}\n\n")

                # 动量分析
                momentum = analysis['momentum']
                f.write("⚡ 动量分析:\n")
                rsi_info = momentum['rsi']
                if rsi_info['current'] is not None:
                    f.write(f"  RSI: {rsi_info['current']:.2f} ({rsi_info['status']})\n")
                else:
                    f.write(f"  RSI: {rsi_info['status']}\n")
                f.write("\n")

                # 波动性分析
                volatility = analysis['volatility']
                f.write("📊 波动性分析:\n")
                f.write(f"  波动性等级: {volatility['level']}\n")
                if volatility['historical_volatility'] > 0:
                    f.write(f"  历史波动率: {volatility['historical_volatility']:.4f}\n")
                if volatility['atr_volatility'] > 0:
                    f.write(f"  ATR波动率: {volatility['atr_volatility']:.4f}\n")
                f.write("\n")

                # 成交量分析
                volume = analysis['volume']
                f.write("📦 成交量分析:\n")
                f.write(f"  成交量状态: {volume['status']}\n")
                f.write(f"  OBV趋势: {volume['obv_trend']}\n")
                f.write("\n")

                f.write("="*80 + "\n")

            return file_path
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            return None
    
    def analyze(self, symbol: str) -> Dict[str, Any]:
        """
        主要分析方法

        Args:
            symbol: 合约代码，格式为 "交易所/合约代码" 如 "CFFEX/IF2509"

        Returns:
            dict: 分析结果
        """
        # 验证输入
        is_valid, error_msg = self.validate_symbol(symbol)
        if not is_valid:
            return {
                'success': False,
                'error': error_msg,
                'symbol': symbol
            }

        # 解析合约代码
        exchange, contract = symbol.split('/')
        exchange, contract = exchange.strip().upper(), contract.strip()

        # 检查可用周期
        periods = self.data_loader.get_available_periods(exchange, contract)

        if not periods:
            return {
                'success': False,
                'error': f'未找到 {symbol} 的任何可用数据',
                'symbol': symbol
            }

        # 分析每个周期
        results = {}
        saved_files = []

        for period in periods:
            try:
                analysis = self.analyze_contract(exchange, contract, period)

                if analysis:
                    # 保存文件
                    file_path = self.save_analysis_to_file(analysis)
                    if file_path:
                        saved_files.append(file_path)
                        results[period] = analysis

            except Exception as e:
                logger.error(f"分析 {period} 失败: {e}")
                continue

        if results:
            return {
                'success': True,
                'symbol': symbol,
                'exchange': exchange,
                'contract': contract,
                'analyzed_periods': list(results.keys()),
                'saved_files': saved_files,
                'results': results
            }
        else:
            return {
                'success': False,
                'error': '所有周期的数据分析都失败',
                'symbol': symbol
            }

    def get_available_symbols(self) -> List[str]:
        """获取所有可用的合约代码"""
        symbols = []

        exchanges = self.data_loader.list_available_exchanges()
        for exchange in exchanges:
            contracts = self.data_loader.list_contracts_by_exchange(exchange)
            for contract in contracts:
                symbols.append(f"{exchange}/{contract}")

        return symbols

    def _describe_trend(self, trend_value: float) -> str:
        """描述趋势"""
        if trend_value > 0.01:
            return "上升"
        elif trend_value < -0.01:
            return "下降"
        else:
            return "震荡"

# 便捷函数
def analyze_futures(symbol: str, data_dir: str = "/Users/<USER>/Downloads/data_index_0704",
                   output_dir: str = "analysis_results") -> Dict[str, Any]:
    """
    便捷的期货分析函数

    Args:
        symbol: 合约代码，格式为 "交易所/合约代码"
        data_dir: 数据目录路径
        output_dir: 输出目录路径

    Returns:
        dict: 分析结果
    """
    analyzer = FuturesAnalyzer(data_dir, output_dir)
    return analyzer.analyze(symbol)
