#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货技术分析系统 v0.2 主程序
简化的输入方式：交易所/合约代码
自动选择K线周期，结果保存到文件
"""

import sys
import os
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from futures_analyzer import FuturesAnalyzer

# 配置日志 - 只记录错误
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysis.log')
    ]
)
logger = logging.getLogger(__name__)

class FuturesAnalysisSystemV2:
    """期货技术分析系统 v0.2"""

    def __init__(self):
        """初始化系统"""
        self.analyzer = FuturesAnalyzer()
        print("🚀 期货技术分析系统 v0.2")
        print("📊 支持格式: 交易所/合约代码 (如: CFFEX/IF2509)")
        print("🔄 自动选择最佳K线周期进行分析")
        print("📁 分析结果保存到文件，不在终端显示")

    def run_analysis(self, symbol: str, periods: int = 200):
        """
        运行技术分析

        Args:
            symbol: 合约代码，格式为 "交易所/合约代码"
            periods: 分析的数据周期数

        Returns:
            dict: 分析结果
        """
        print(f"🔍 开始分析: {symbol}")

        result = self.analyzer.analyze(symbol, periods, save_to_file=True)

        if result['success']:
            print(f"✅ 分析完成！")
            print(f"📊 分析的周期: {', '.join(result['analyzed_periods'])}")
            print(f"📁 保存的文件:")
            for file_path in result['saved_files']:
                print(f"   - {file_path}")
            return result
        else:
            print(f"❌ 分析失败: {result['error']}")
            return None

    def show_available_symbols(self):
        """显示所有可用的合约代码"""
        symbols = self.analyzer.get_available_symbols()

        print(f"\n📋 可用合约代码 (格式: 交易所/合约代码):")
        print("-" * 60)

        for exchange, contracts in symbols.items():
            print(f"\n🏢 {exchange} 交易所:")
            for i, symbol in enumerate(contracts[:5], 1):  # 只显示前5个
                print(f"  {i}. {symbol}")
            if len(contracts) > 5:
                print(f"  ... 还有 {len(contracts) - 5} 个合约")

        return symbols

    def validate_and_show_info(self, symbol: str):
        """验证并显示合约信息"""
        if not self.analyzer.validate_symbol(symbol):
            print(f"❌ 无效的合约代码: {symbol}")
            print("💡 正确格式: 交易所/合约代码，如 CFFEX/IF2509")
            return False

        try:
            exchange, contract = self.analyzer.parse_symbol(symbol)
            availability = self.analyzer.check_data_availability(exchange, contract)

            print(f"\n📊 合约信息: {symbol}")
            print("-" * 40)
            print(f"交易所: {exchange}")
            print(f"合约代码: {contract}")
            print(f"可用数据: {', '.join(availability['available_periods'])}")

            return True
        except Exception as e:
            print(f"❌ 获取合约信息失败: {e}")
            return False

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🚀 期货技术分析系统 v0.2")
    print("="*60)
    print("1. 查看可用合约代码")
    print("2. 验证合约代码")
    print("3. 进行技术分析")
    print("4. 批量分析")
    print("0. 退出系统")
    print("-"*60)

def main():
    """主程序"""
    system = FuturesAnalysisSystemV2()

    print("\n💡 使用说明:")
    print("  - 合约代码格式: 交易所/合约代码 (如: CFFEX/IF2509)")
    print("  - 系统自动选择最佳K线周期进行分析")
    print("  - 分析结果保存到 analysis_results 文件夹")

    while True:
        show_menu()

        try:
            choice = input("请选择功能 (0-4): ").strip()

            if choice == '0':
                print("👋 感谢使用，再见！")
                break

            elif choice == '1':
                # 查看可用合约代码
                system.show_available_symbols()

            elif choice == '2':
                # 验证合约代码
                symbol = input("请输入合约代码 (如 CFFEX/IF2509): ").strip()
                if symbol:
                    system.validate_and_show_info(symbol)
                else:
                    print("❌ 请输入有效的合约代码")

            elif choice == '3':
                # 进行技术分析
                symbol = input("请输入合约代码 (如 CFFEX/IF2509): ").strip()
                if symbol:
                    periods_input = input("请输入分析周期数 (默认200): ").strip()
                    periods = int(periods_input) if periods_input.isdigit() else 200

                    result = system.run_analysis(symbol, periods)
                else:
                    print("❌ 请输入有效的合约代码")

            elif choice == '4':
                # 批量分析
                print("📊 批量分析功能")
                symbols_input = input("请输入合约代码列表 (用逗号分隔，如 CFFEX/IF2509,SHFE/ag2508): ").strip()

                if symbols_input:
                    symbols = [s.strip() for s in symbols_input.split(',')]
                    periods_input = input("请输入分析周期数 (默认200): ").strip()
                    periods = int(periods_input) if periods_input.isdigit() else 200

                    print(f"\n🔍 开始批量分析 {len(symbols)} 个合约...")

                    success_count = 0
                    for i, symbol in enumerate(symbols, 1):
                        print(f"\n📊 [{i}/{len(symbols)}] 分析 {symbol}...")
                        result = system.run_analysis(symbol, periods)
                        if result:
                            success_count += 1
                        print("-" * 60)

                    print(f"\n✅ 批量分析完成！成功分析 {success_count}/{len(symbols)} 个合约")
                else:
                    print("❌ 请输入有效的合约代码列表")

            else:
                print("❌ 无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"❌ 发生错误: {str(e)}")
            logger.error(f"主程序错误: {str(e)}")

if __name__ == "__main__":
    main()
